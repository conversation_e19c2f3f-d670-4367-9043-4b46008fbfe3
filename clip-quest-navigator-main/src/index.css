@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 95%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 40%;

    --accent: 0 0% 95%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 0% 20%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 0 0% 20%;

    --sidebar-primary: 0 0% 0%;

    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 0 0% 95%;

    --sidebar-accent-foreground: 0 0% 0%;

    --sidebar-border: 0 0% 90%;

    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 60%;

    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 0% 80%;
    --destructive-foreground: 0 0% 0%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 100%;
    --sidebar-background: 0 0% 5%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 10%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles for visual search */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-500\/50 {
    scrollbar-color: rgba(128, 128, 128, 0.5) transparent;
  }

  .scrollbar-track-transparent {
    scrollbar-color: rgba(128, 128, 128, 0.5) transparent;
  }

  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.5);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(128, 128, 128, 0.7);
  }
}