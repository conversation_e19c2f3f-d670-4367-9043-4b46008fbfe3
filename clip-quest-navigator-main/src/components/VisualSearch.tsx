import React, { useState } from 'react';
import { Search, Eye, Clock, Filter, Zap, Film } from 'lucide-react';
import { apiService, SearchResult, ClipResult } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface VisualSearchProps {
  videoId: number;
  videoUrl: string;
  onTimeJump: (time: number) => void;
}

const VisualSearch: React.FC<VisualSearchProps> = ({ videoId, videoUrl, onTimeJump }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchClips, setSearchClips] = useState<ClipResult[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [viewMode, setViewMode] = useState<'clips' | 'frames'>('clips');
  const { toast } = useToast();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    try {
      const response = await apiService.visualSearch(videoId, searchQuery);
      setSearchResults(response.results);
      setSearchClips(response.clips || []);

      if (response.results.length === 0) {
        toast({
          title: "No results found",
          description: `No visual content matching "${searchQuery}" was found in this video.`,
        });
      }
    } catch (error) {
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search video content",
        variant: "destructive",
      });

      // Fallback to mock results for demo
      const mockResults: SearchResult[] = generateMockResults(searchQuery);
      setSearchResults(mockResults);
      setSearchClips([]); // No clips for mock data
    } finally {
      setIsSearching(false);
    }
  };

  const generateMockResults = (query: string): SearchResult[] => {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('person') || lowerQuery.includes('speaker') || lowerQuery.includes('man')) {
      return [
        {
          timestamp: 15,
          confidence: 95,
          description: 'Speaker introducing himself at the beginning',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 45,
          confidence: 92,
          description: 'Speaker explaining his background',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 180,
          confidence: 88,
          description: 'Speaker demonstrating the project features',
          frame_path: '/api/placeholder/120/68'
        }
      ];
    }

    if (lowerQuery.includes('screen') || lowerQuery.includes('display') || lowerQuery.includes('computer')) {
      return [
        {
          timestamp: 120,
          confidence: 89,
          description: 'Computer screen showing project overview',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 240,
          confidence: 85,
          description: 'Display showing video analysis interface',
          frame_path: '/api/placeholder/120/68'
        }
      ];
    }

    if (lowerQuery.includes('text') || lowerQuery.includes('code') || lowerQuery.includes('diagram')) {
      return [
        {
          timestamp: 90,
          confidence: 82,
          description: 'Text showing project requirements',
          frame_path: '/api/placeholder/120/68'
        },
        {
          timestamp: 200,
          confidence: 78,
          description: 'Code examples and implementation details',
          frame_path: '/api/placeholder/120/68'
        }
      ];
    }

    // Default results for any query
    return [
      {
        timestamp: 60,
        confidence: 75,
        description: `Content related to "${query}" found in video`,
        frame_path: '/api/placeholder/120/68'
      },
      {
        timestamp: 150,
        confidence: 68,
        description: `Another instance of "${query}" detected`,
        frame_path: '/api/placeholder/120/68'
      }
    ];
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 75) return 'text-yellow-400';
    return 'text-orange-400';
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-foreground mb-2">Visual Search</h3>
        <p className="text-sm text-muted-foreground">
          Search for objects, people, or scenes within video frames
        </p>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Describe what you're looking for... (e.g., 'red car', 'person speaking', 'computer screen')"
            className="w-full px-4 py-3 pl-12 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            disabled={isSearching}
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
        </div>

        <button
          type="submit"
          disabled={!searchQuery.trim() || isSearching}
          className="w-full bg-primary hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
        >
          {isSearching ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-foreground border-t-transparent"></div>
              Analyzing Frames...
            </>
          ) : (
            <>
              <Eye className="w-5 h-5" />
              Search Video
            </>
          )}
        </button>
      </form>

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-pulse space-y-4">
                <div className="w-16 h-16 bg-muted rounded-full mx-auto flex items-center justify-center">
                  <Zap className="w-8 h-8 text-foreground" />
                </div>
                <p className="text-muted-foreground">
                  AI is analyzing video frames for "{searchQuery}"
                </p>
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            <>
              {/* Results Header with View Toggle */}
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="text-foreground font-medium">
                    Found {searchResults.length} matches for "{searchQuery}"
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click any result to jump to that moment in the video
                  </p>
                </div>
                {searchClips.length > 0 && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setViewMode('clips')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'clips'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                      }`}
                    >
                      <Film className="w-4 h-4 inline mr-1" />
                      Clips ({searchClips.length})
                    </button>
                    <button
                      onClick={() => setViewMode('frames')}
                      className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                        viewMode === 'frames'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                      }`}
                    >
                      <Eye className="w-4 h-4 inline mr-1" />
                      Frames ({searchResults.length})
                    </button>
                  </div>
                )}
              </div>

              {/* Clips View */}
              {viewMode === 'clips' && searchClips.length > 0 && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchClips.map((clip, index) => (
                    <div
                      key={index}
                      className="bg-card rounded-lg border border-border p-4 hover:bg-muted/50 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 pr-4">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs text-muted-foreground font-medium">#{index + 1}</span>
                            <h5 className="text-foreground font-medium">
                              {formatTime(clip.start_time)} - {formatTime(clip.end_time)}
                            </h5>
                            <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                              {Math.round((clip.end_time - clip.start_time) * 10) / 10}s
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {clip.description}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <span className={`text-sm font-medium px-2 py-1 rounded ${getConfidenceColor(clip.confidence)} bg-muted`}>
                            {clip.confidence}%
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {clip.frame_count} frames
                          </span>
                        </div>
                      </div>

                      {/* Clip Actions */}
                      <div className="flex gap-2">
                        <button
                          onClick={() => onTimeJump(clip.start_time)}
                          className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1"
                        >
                          <Eye className="w-4 h-4" />
                          Play from start
                        </button>
                        <button
                          onClick={() => setViewMode('frames')}
                          className="bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground px-3 py-2 rounded text-sm font-medium transition-colors flex items-center gap-1"
                        >
                          <Film className="w-4 h-4" />
                          View frames
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Frames View */}
              {(viewMode === 'frames' || searchClips.length === 0) && (
                <div className="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-gray-500/50 scrollbar-track-transparent">
                  {searchResults.map((result, index) => (
                    <div
                      key={index}
                      className="bg-card rounded-lg border border-border p-4 hover:bg-muted/50 transition-all duration-300 cursor-pointer group"
                      onClick={() => onTimeJump(result.timestamp)}
                    >
                      <div className="flex gap-4">
                        {/* Frame Number & Thumbnail */}
                        <div className="flex flex-col items-center gap-2 flex-shrink-0">
                          <div className="w-20 h-12 bg-muted rounded flex items-center justify-center relative overflow-hidden">
                            {result.frame_path && result.frame_path !== '/api/placeholder/120/68' ? (
                              <img
                                src={result.frame_path}
                                alt={`Frame at ${formatTime(result.timestamp)}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallback = target.nextElementSibling as HTMLElement;
                                  if (fallback) fallback.style.display = 'flex';
                                }}
                              />
                            ) : null}
                            <div
                              className="w-full h-full flex items-center justify-center"
                              style={{ display: (result.frame_path && result.frame_path !== '/api/placeholder/120/68') ? 'none' : 'flex' }}
                            >
                              <Eye className="w-6 h-6 text-muted-foreground" />
                            </div>
                          </div>
                          <span className="text-xs text-muted-foreground font-medium">#{index + 1}</span>
                        </div>

                        {/* Content */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 pr-2">
                              <p className="text-foreground text-sm leading-relaxed group-hover:text-muted-foreground transition-colors">
                                {result.description}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 text-xs">
                              <span className={`font-medium px-2 py-1 rounded ${getConfidenceColor(result.confidence)} bg-muted`}>
                                {result.confidence}%
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              <span className="font-medium">{formatTime(result.timestamp)}</span>
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onTimeJump(result.timestamp);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-opacity bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1 rounded text-xs font-medium flex items-center gap-1"
                            >
                              <Eye className="w-3 h-3" />
                              Jump to frame
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-foreground mb-2">
                No matches found for "{searchQuery}"
              </p>
              <p className="text-sm text-muted-foreground">
                Try different keywords or be more specific about visual elements
              </p>
            </div>
          )}
        </div>
      )}

      {/* Search Tips */}
      {!hasSearched && (
        <div className="mt-6 p-4 bg-card rounded-lg border border-border">
          <h4 className="text-sm font-medium text-foreground mb-3">Search Tips</h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>• Try specific objects: "red car", "laptop", "coffee cup"</li>
            <li>• Search for people: "person speaking", "woman in blue shirt"</li>
            <li>• Look for scenes: "outdoor scene", "office setting", "sunset"</li>
            <li>• Find text: "code on screen", "presentation slide", "whiteboard"</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default VisualSearch;
